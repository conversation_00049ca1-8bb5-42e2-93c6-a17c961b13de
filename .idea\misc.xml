<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CheckStyle-IDEA" serialisationVersion="2">
    <checkstyleVersion>10.23.0</checkstyleVersion>
    <scanScope>JavaOnly</scanScope>
    <copyLibs>true</copyLibs>
    <option name="locations">
      <list>
        <ConfigurationLocation id="bundled-sun-checks" type="BUNDLED" scope="All" description="Sun Checks">(bundled)</ConfigurationLocation>
        <ConfigurationLocation id="bundled-google-checks" type="BUNDLED" scope="All" description="Google Checks">(bundled)</ConfigurationLocation>
      </list>
    </option>
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="KubernetesApiProvider"><![CDATA[{}]]></component>
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State>
            <id>SQL</id>
          </State>
        </expanded-state>
        <selected-state>
          <State>
            <id>User defined</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" default="true" project-jdk-name="1.8" project-jdk-type="JavaSDK" />
</project>